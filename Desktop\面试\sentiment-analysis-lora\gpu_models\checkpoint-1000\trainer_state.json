{"best_global_step": 1000, "best_metric": 0.35408923279552185, "best_model_checkpoint": "./gpu_models\\checkpoint-1000", "epoch": 1.0, "eval_steps": 500, "global_step": 1000, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.05, "grad_norm": NaN, "learning_rate": 9.8e-05, "loss": 0.647, "step": 50}, {"epoch": 0.1, "grad_norm": NaN, "learning_rate": 0.00019800000000000002, "loss": 0.0, "step": 100}, {"epoch": 0.15, "grad_norm": NaN, "learning_rate": 0.0001998591477024944, "loss": 0.0, "step": 150}, {"epoch": 0.2, "grad_norm": NaN, "learning_rate": 0.00019942544999705569, "loss": 0.0, "step": 200}, {"epoch": 0.25, "grad_norm": NaN, "learning_rate": 0.00019870012031599407, "loss": 0.0, "step": 250}, {"epoch": 0.3, "grad_norm": NaN, "learning_rate": 0.00019768528617623675, "loss": 0.0, "step": 300}, {"epoch": 0.35, "grad_norm": NaN, "learning_rate": 0.00019638392426116926, "loss": 0.0, "step": 350}, {"epoch": 0.4, "grad_norm": NaN, "learning_rate": 0.0001947998516895103, "loss": 0.0, "step": 400}, {"epoch": 0.45, "grad_norm": NaN, "learning_rate": 0.00019293771481904463, "loss": 0.0, "step": 450}, {"epoch": 0.5, "grad_norm": NaN, "learning_rate": 0.00019080297561805534, "loss": 0.0, "step": 500}, {"epoch": 0.55, "grad_norm": NaN, "learning_rate": 0.0001884018956444297, "loss": 0.0, "step": 550}, {"epoch": 0.6, "grad_norm": NaN, "learning_rate": 0.000185741517679431, "loss": 0.0, "step": 600}, {"epoch": 0.65, "grad_norm": NaN, "learning_rate": 0.0001828296450700078, "loss": 0.0, "step": 650}, {"epoch": 0.7, "grad_norm": NaN, "learning_rate": 0.00017967481884023257, "loss": 0.0, "step": 700}, {"epoch": 0.75, "grad_norm": NaN, "learning_rate": 0.00017628629263900675, "loss": 0.0, "step": 750}, {"epoch": 0.8, "grad_norm": NaN, "learning_rate": 0.00017267400559751396, "loss": 0.0, "step": 800}, {"epoch": 0.85, "grad_norm": NaN, "learning_rate": 0.00016884855317603588, "loss": 0.0, "step": 850}, {"epoch": 0.9, "grad_norm": NaN, "learning_rate": 0.0001648211560856415, "loss": 0.0, "step": 900}, {"epoch": 0.95, "grad_norm": NaN, "learning_rate": 0.00016060362737590843, "loss": 0.0, "step": 950}, {"epoch": 1.0, "grad_norm": NaN, "learning_rate": 0.00015620833778521307, "loss": 0.0, "step": 1000}, {"epoch": 1.0, "eval_accuracy": 0.5185, "eval_f1": 0.35408923279552185, "eval_loss": NaN, "eval_precision": 0.26884225, "eval_recall": 0.5185, "eval_runtime": 14.1627, "eval_samples_per_second": 141.216, "eval_steps_per_second": 8.826, "step": 1000}], "logging_steps": 50, "max_steps": 3000, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 2170797834240000.0, "train_batch_size": 16, "trial_name": null, "trial_params": null}