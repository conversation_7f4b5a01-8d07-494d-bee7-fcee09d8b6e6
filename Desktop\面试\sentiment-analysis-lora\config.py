"""
项目配置文件
包含数据集、模型、训练等配置选项
"""

# 数据集配置
DATASET_CONFIGS = {
    "ChnSentiCorp": {
        "name": "seamew/ChnSentiCorp",
        "description": "中文情感语料库 - 酒店、笔记本、书籍评论",
        "size": "约12K条",
        "labels": "二分类 (0:负面, 1:正面)",
        "language": "中文",
        "recommended": True
    },
    "emotion": {
        "name": "emotion",
        "description": "多语言情感数据集",
        "size": "约16K条",
        "labels": "6分类 (sadness, joy, love, anger, fear, surprise)",
        "language": "英文",
        "recommended": False
    },
    "sample": {
        "name": "sample",
        "description": "内置示例数据集",
        "size": "30条",
        "labels": "三分类 (0:负面, 1:正面, 2:中性)",
        "language": "中文",
        "recommended": False
    }
}

# 模型配置
MODEL_CONFIGS = {
    "bert-base-chinese": {
        "description": "Google BERT中文基础模型",
        "size": "约400MB",
        "performance": "高",
        "speed": "中等",
        "recommended": True
    },
    "hfl/chinese-bert-wwm-ext": {
        "description": "哈工大BERT中文全词掩码模型",
        "size": "约400MB", 
        "performance": "很高",
        "speed": "中等",
        "recommended": True
    },
    "hfl/chinese-roberta-wwm-ext": {
        "description": "哈工大RoBERTa中文模型",
        "size": "约400MB",
        "performance": "很高",
        "speed": "快",
        "recommended": False
    }
}

# 训练配置
TRAINING_CONFIGS = {
    "quick_test": {
        "description": "快速测试配置",
        "num_epochs": 1,
        "batch_size": 4,
        "learning_rate": 2e-4,
        "max_length": 256,
        "recommended_for": "快速验证代码"
    },
    "standard": {
        "description": "标准训练配置",
        "num_epochs": 3,
        "batch_size": 8,
        "learning_rate": 2e-4,
        "max_length": 512,
        "recommended_for": "一般项目"
    },
    "high_quality": {
        "description": "高质量训练配置",
        "num_epochs": 5,
        "batch_size": 16,
        "learning_rate": 1e-4,
        "max_length": 512,
        "recommended_for": "生产环境"
    }
}

# LoRA配置
LORA_CONFIGS = {
    "light": {
        "description": "轻量级LoRA配置",
        "r": 8,
        "lora_alpha": 16,
        "lora_dropout": 0.1,
        "target_modules": ["query", "value"],
        "recommended_for": "资源受限环境"
    },
    "standard": {
        "description": "标准LoRA配置",
        "r": 16,
        "lora_alpha": 32,
        "lora_dropout": 0.1,
        "target_modules": ["query", "value", "key", "dense"],
        "recommended_for": "一般项目"
    },
    "heavy": {
        "description": "重型LoRA配置",
        "r": 32,
        "lora_alpha": 64,
        "lora_dropout": 0.05,
        "target_modules": ["query", "value", "key", "dense", "intermediate"],
        "recommended_for": "追求最佳性能"
    }
}

# 默认配置
DEFAULT_CONFIG = {
    "dataset": "ChnSentiCorp",
    "model": "bert-base-chinese", 
    "training": "standard",
    "lora": "standard"
}

def get_config(config_type: str, config_name: str = None):
    """获取配置"""
    configs = {
        "dataset": DATASET_CONFIGS,
        "model": MODEL_CONFIGS,
        "training": TRAINING_CONFIGS,
        "lora": LORA_CONFIGS
    }
    
    if config_type not in configs:
        raise ValueError(f"未知配置类型: {config_type}")
    
    if config_name is None:
        return configs[config_type]
    
    if config_name not in configs[config_type]:
        raise ValueError(f"未知配置名称: {config_name}")
    
    return configs[config_type][config_name]

def print_available_configs():
    """打印所有可用配置"""
    print("=== 可用配置 ===\n")
    
    print("📊 数据集配置:")
    for name, config in DATASET_CONFIGS.items():
        mark = "⭐" if config.get("recommended") else "  "
        print(f"{mark} {name}: {config['description']}")
        print(f"     大小: {config['size']}, 标签: {config['labels']}")
    
    print("\n🤖 模型配置:")
    for name, config in MODEL_CONFIGS.items():
        mark = "⭐" if config.get("recommended") else "  "
        print(f"{mark} {name}: {config['description']}")
        print(f"     大小: {config['size']}, 性能: {config['performance']}")
    
    print("\n🏋️ 训练配置:")
    for name, config in TRAINING_CONFIGS.items():
        print(f"   {name}: {config['description']}")
        print(f"     轮数: {config['num_epochs']}, 批次: {config['batch_size']}")
    
    print("\n🔧 LoRA配置:")
    for name, config in LORA_CONFIGS.items():
        print(f"   {name}: {config['description']}")
        print(f"     rank: {config['r']}, alpha: {config['lora_alpha']}")

if __name__ == "__main__":
    print_available_configs()
    
    print(f"\n默认配置:")
    print(f"  数据集: {DEFAULT_CONFIG['dataset']}")
    print(f"  模型: {DEFAULT_CONFIG['model']}")
    print(f"  训练: {DEFAULT_CONFIG['training']}")
    print(f"  LoRA: {DEFAULT_CONFIG['lora']}")
