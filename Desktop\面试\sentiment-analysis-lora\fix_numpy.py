"""
修复NumPy兼容性问题
"""

import subprocess
import sys

def fix_numpy_compatibility():
    """修复NumPy兼容性"""
    
    print("=== 修复NumPy兼容性问题 ===")
    
    try:
        import numpy as np
        print(f"当前NumPy版本: {np.__version__}")
        
        if np.__version__.startswith('2.'):
            print("检测到NumPy 2.x，需要降级到1.x")
            
            # 降级NumPy
            print("正在降级NumPy...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "numpy<2"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ NumPy降级成功")
                
                # 验证修复
                print("验证修复...")
                import importlib
                importlib.reload(np)
                
                import torch
                print(f"PyTorch版本: {torch.__version__}")
                print(f"CUDA可用: {torch.cuda.is_available()}")
                
                if torch.cuda.is_available():
                    print(f"GPU: {torch.cuda.get_device_name(0)}")
                    print("🎉 修复成功！现在可以正常训练了")
                    return True
                else:
                    print("❌ CUDA仍不可用")
                    return False
            else:
                print(f"❌ NumPy降级失败: {result.stderr}")
                return False
        else:
            print("✅ NumPy版本正常，无需修复")
            return True
            
    except Exception as e:
        print(f"❌ 修复过程出错: {e}")
        return False

if __name__ == "__main__":
    success = fix_numpy_compatibility()
    
    if success:
        print("\n🚀 现在可以开始GPU训练了:")
        print("   python gpu_train.py")
    else:
        print("\n手动修复命令:")
        print("   pip install \"numpy<2\"")
        print("   python check_cuda.py")
