"""
快速训练脚本 - 优化CPU训练速度
"""

import os
import sys
sys.path.append('./src')

from train import train_model

def fast_cpu_training():
    """CPU优化的快速训练"""
    
    print("=== 快速CPU训练配置 ===")
    print("优化策略:")
    print("- 减少数据量 (使用子集)")
    print("- 增大批次大小")
    print("- 减少序列长度")
    print("- 减少训练轮数")
    
    # 创建快速训练目录
    output_dir = "./fast_models"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        trainer, results = train_model(
            model_name="bert-base-chinese",
            output_dir=output_dir,
            num_epochs=2,  # 减少到2轮
            batch_size=16,  # 增大批次 (CPU可以处理)
            learning_rate=3e-4,  # 稍微提高学习率
            max_length=256,  # 减少序列长度
            data_source="huggingface",
            dataset_name="emotion"
        )
        
        print("\n🎉 快速训练完成！")
        print(f"最终结果: {results}")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        return False

if __name__ == "__main__":
    print("开始快速CPU训练...")
    success = fast_cpu_training()
    
    if success:
        print("\n✅ 训练成功完成！")
        print("下一步可以:")
        print("1. 运行 python src/evaluate.py 评估模型")
        print("2. 运行 python app.py 启动Web应用")
    else:
        print("\n❌ 训练失败")
