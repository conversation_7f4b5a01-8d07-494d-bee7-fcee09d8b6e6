"""
模型推理脚本
提供简单的推理接口
"""

import torch
import numpy as np
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from peft import PeftModel
import json
import os


class SentimentPredictor:
    """情感分析预测器"""
    
    def __init__(self, model_path: str, base_model_name: str = "bert-base-chinese"):
        self.model_path = model_path
        self.base_model_name = base_model_name
        self.class_names = ['负面', '正面', '中性']
        self.model = None
        self.tokenizer = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        self.load_model()
    
    def load_model(self):
        """加载模型"""
        print(f"正在加载模型... (设备: {self.device})")
        
        # 加载tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
        
        # 加载基础模型
        base_model = AutoModelForSequenceClassification.from_pretrained(
            self.base_model_name,
            num_labels=3,
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
        )
        
        # 加载LoRA权重
        self.model = PeftModel.from_pretrained(base_model, self.model_path)
        self.model = self.model.to(self.device)
        self.model.eval()
        
        print("模型加载完成！")
    
    def predict(self, text: str, max_length: int = 512):
        """预测单个文本的情感"""
        
        # 文本编码
        inputs = self.tokenizer(
            text,
            truncation=True,
            padding=True,
            max_length=max_length,
            return_tensors='pt'
        ).to(self.device)
        
        # 预测
        with torch.no_grad():
            outputs = self.model(**inputs)
            logits = outputs.logits
            probabilities = torch.softmax(logits, dim=-1)
            prediction = torch.argmax(logits, dim=-1)
        
        # 解析结果
        pred_class_idx = prediction.item()
        pred_class = self.class_names[pred_class_idx]
        confidence = probabilities[0][pred_class_idx].item()
        
        # 所有类别的概率
        all_probs = {
            class_name: prob.item() 
            for class_name, prob in zip(self.class_names, probabilities[0])
        }
        
        return {
            'text': text,
            'predicted_class': pred_class,
            'confidence': confidence,
            'all_probabilities': all_probs
        }
    
    def predict_batch(self, texts: list, max_length: int = 512, batch_size: int = 8):
        """批量预测"""
        results = []
        
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i+batch_size]
            
            # 编码
            inputs = self.tokenizer(
                batch_texts,
                truncation=True,
                padding=True,
                max_length=max_length,
                return_tensors='pt'
            ).to(self.device)
            
            # 预测
            with torch.no_grad():
                outputs = self.model(**inputs)
                logits = outputs.logits
                probabilities = torch.softmax(logits, dim=-1)
                predictions = torch.argmax(logits, dim=-1)
            
            # 解析结果
            for j, text in enumerate(batch_texts):
                pred_class_idx = predictions[j].item()
                pred_class = self.class_names[pred_class_idx]
                confidence = probabilities[j][pred_class_idx].item()
                
                all_probs = {
                    class_name: probabilities[j][k].item() 
                    for k, class_name in enumerate(self.class_names)
                }
                
                results.append({
                    'text': text,
                    'predicted_class': pred_class,
                    'confidence': confidence,
                    'all_probabilities': all_probs
                })
        
        return results


def main():
    """主函数 - 交互式预测"""

    # 检查可用的模型路径 (从src目录运行)
    possible_paths = ["../gpu_models", "../models", "../fast_models", "./models"]
    model_path = None

    for path in possible_paths:
        if os.path.exists(path) and os.path.exists(os.path.join(path, "adapter_config.json")):
            model_path = path
            print(f"使用模型: {model_path}")
            break

    if not model_path:
        print("❌ 未找到训练好的模型")
        print("检查的路径:")
        for path in possible_paths:
            exists = "✅" if os.path.exists(path) else "❌"
            print(f"  {exists} {path}")
        print("请先运行训练脚本生成模型")
        return
    
    # 初始化预测器
    try:
        predictor = SentimentPredictor(model_path)
    except Exception as e:
        print(f"模型加载失败: {e}")
        return
    
    print("\n=== 中文情感分析系统 ===")
    print("输入文本进行情感分析，输入 'quit' 退出")
    print("支持的情感类别: 正面、负面、中性")
    print("-" * 50)
    
    while True:
        try:
            # 获取用户输入
            text = input("\n请输入要分析的文本: ").strip()
            
            if text.lower() in ['quit', 'exit', '退出', 'q']:
                print("再见！")
                break
            
            if not text:
                print("请输入有效的文本")
                continue
            
            # 预测
            result = predictor.predict(text)
            
            # 显示结果
            print(f"\n输入文本: {result['text']}")
            print(f"预测结果: {result['predicted_class']}")
            print(f"置信度: {result['confidence']:.4f}")
            
            print("\n各类别概率:")
            for class_name, prob in result['all_probabilities'].items():
                print(f"  {class_name}: {prob:.4f}")
            
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"预测过程中出现错误: {e}")


def demo_batch_prediction():
    """演示批量预测"""

    # 检查可用的模型路径 (从src目录运行)
    possible_paths = ["../gpu_models", "../models", "../fast_models", "./models"]
    model_path = None

    for path in possible_paths:
        if os.path.exists(path) and os.path.exists(os.path.join(path, "adapter_config.json")):
            model_path = path
            break

    if not model_path:
        print("❌ 未找到训练好的模型")
        return
    
    # 初始化预测器
    predictor = SentimentPredictor(model_path)
    
    # 测试文本
    test_texts = [
        "这个产品质量很好，非常满意！",
        "服务态度差，不推荐购买",
        "价格合理，性价比不错",
        "物流太慢了，很失望",
        "包装精美，产品质量优秀",
        "客服回复及时，解决问题很快",
        "产品有瑕疵，需要改进",
        "整体还可以，符合预期"
    ]
    
    print("=== 批量预测演示 ===")
    results = predictor.predict_batch(test_texts)
    
    for i, result in enumerate(results, 1):
        print(f"\n{i}. 文本: {result['text']}")
        print(f"   预测: {result['predicted_class']} (置信度: {result['confidence']:.3f})")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        demo_batch_prediction()
    else:
        main()
