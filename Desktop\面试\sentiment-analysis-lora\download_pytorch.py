"""
手动下载PyTorch CUDA版本
"""

import urllib.request
import os
import sys

def download_file(url, filename):
    """下载文件并显示进度"""
    
    def progress_hook(block_num, block_size, total_size):
        downloaded = block_num * block_size
        if total_size > 0:
            percent = min(100, (downloaded * 100) // total_size)
            size_mb = total_size / (1024 * 1024)
            downloaded_mb = downloaded / (1024 * 1024)
            print(f"\r下载 {filename}: {percent}% ({downloaded_mb:.1f}/{size_mb:.1f}MB)", end="")
        else:
            downloaded_mb = downloaded / (1024 * 1024)
            print(f"\r下载 {filename}: {downloaded_mb:.1f}MB", end="")
    
    try:
        print(f"开始下载: {filename}")
        urllib.request.urlretrieve(url, filename, progress_hook)
        print(f"\n✅ {filename} 下载完成")
        return True
    except Exception as e:
        print(f"\n❌ {filename} 下载失败: {e}")
        return False

def get_python_version():
    """获取Python版本"""
    version = sys.version_info
    return f"cp{version.major}{version.minor}"

def download_pytorch_cuda():
    """下载PyTorch CUDA版本"""
    
    print("=== PyTorch CUDA版本下载器 ===")
    
    # 检测Python版本
    py_version = get_python_version()
    print(f"检测到Python版本: {py_version}")
    
    # 创建下载目录
    download_dir = "./pytorch_wheels"
    os.makedirs(download_dir, exist_ok=True)
    
    # 根据Python版本选择下载链接
    if py_version == "cp311":  # Python 3.11
        files = {
            "torch-2.1.0+cu118-cp311-cp311-win_amd64.whl": 
                "https://download.pytorch.org/whl/cu118/torch-2.1.0%2Bcu118-cp311-cp311-win_amd64.whl",
            "torchvision-0.16.0+cu118-cp311-cp311-win_amd64.whl":
                "https://download.pytorch.org/whl/cu118/torchvision-0.16.0%2Bcu118-cp311-cp311-win_amd64.whl",
            "torchaudio-2.1.0+cu118-cp311-cp311-win_amd64.whl":
                "https://download.pytorch.org/whl/cu118/torchaudio-2.1.0%2Bcu118-cp311-cp311-win_amd64.whl"
        }
    elif py_version == "cp310":  # Python 3.10
        files = {
            "torch-2.1.0+cu118-cp310-cp310-win_amd64.whl":
                "https://download.pytorch.org/whl/cu118/torch-2.1.0%2Bcu118-cp310-cp310-win_amd64.whl",
            "torchvision-0.16.0+cu118-cp310-cp310-win_amd64.whl":
                "https://download.pytorch.org/whl/cu118/torchvision-0.16.0%2Bcu118-cp310-cp310-win_amd64.whl",
            "torchaudio-2.1.0+cu118-cp310-cp310-win_amd64.whl":
                "https://download.pytorch.org/whl/cu118/torchaudio-2.1.0%2Bcu118-cp310-cp310-win_amd64.whl"
        }
    elif py_version == "cp39":  # Python 3.9
        files = {
            "torch-2.1.0+cu118-cp39-cp39-win_amd64.whl":
                "https://download.pytorch.org/whl/cu118/torch-2.1.0%2Bcu118-cp39-cp39-win_amd64.whl",
            "torchvision-0.16.0+cu118-cp39-cp39-win_amd64.whl":
                "https://download.pytorch.org/whl/cu118/torchvision-0.16.0%2Bcu118-cp39-cp39-win_amd64.whl",
            "torchaudio-2.1.0+cu118-cp39-cp39-win_amd64.whl":
                "https://download.pytorch.org/whl/cu118/torchaudio-2.1.0%2Bcu118-cp39-cp39-win_amd64.whl"
        }
    else:
        print(f"❌ 不支持的Python版本: {py_version}")
        print("支持的版本: Python 3.9, 3.10, 3.11")
        return False
    
    # 下载文件
    success_count = 0
    for filename, url in files.items():
        filepath = os.path.join(download_dir, filename)
        
        # 检查文件是否已存在
        if os.path.exists(filepath):
            print(f"✅ {filename} 已存在，跳过下载")
            success_count += 1
            continue
        
        if download_file(url, filepath):
            success_count += 1
    
    print(f"\n下载完成: {success_count}/{len(files)} 个文件")
    
    if success_count == len(files):
        print("\n🎉 所有文件下载成功!")
        print("\n下一步安装命令:")
        print("1. 卸载旧版本:")
        print("   pip uninstall torch torchvision torchaudio -y")
        print("\n2. 安装下载的文件:")
        for filename in files.keys():
            print(f"   pip install {download_dir}/{filename}")
        
        print("\n或者运行自动安装:")
        print("   python install_downloaded.py")
        
        return True
    else:
        print("\n❌ 部分文件下载失败")
        return False

def create_install_script():
    """创建自动安装脚本"""
    
    py_version = get_python_version()
    
    script_content = f'''"""
自动安装下载的PyTorch文件
"""

import subprocess
import os

def install_pytorch():
    """安装下载的PyTorch文件"""
    
    download_dir = "./pytorch_wheels"
    
    # 卸载旧版本
    print("卸载旧版本...")
    subprocess.run("pip uninstall torch torchvision torchaudio -y", shell=True)
    
    # 安装新版本
    files = [
        "torch-2.1.0+cu118-{py_version}-{py_version}-win_amd64.whl",
        "torchvision-0.16.0+cu118-{py_version}-{py_version}-win_amd64.whl", 
        "torchaudio-2.1.0+cu118-{py_version}-{py_version}-win_amd64.whl"
    ]
    
    for filename in files:
        filepath = os.path.join(download_dir, filename)
        if os.path.exists(filepath):
            print(f"安装 {{filename}}...")
            result = subprocess.run(f"pip install {{filepath}}", shell=True)
            if result.returncode == 0:
                print(f"✅ {{filename}} 安装成功")
            else:
                print(f"❌ {{filename}} 安装失败")
        else:
            print(f"❌ 文件不存在: {{filepath}}")
    
    # 验证安装
    print("\\n验证安装...")
    try:
        import torch
        print(f"PyTorch版本: {{torch.__version__}}")
        print(f"CUDA可用: {{torch.cuda.is_available()}}")
        if torch.cuda.is_available():
            print(f"GPU: {{torch.cuda.get_device_name(0)}}")
            print("🎉 CUDA安装成功!")
        else:
            print("❌ CUDA不可用")
    except ImportError:
        print("❌ PyTorch导入失败")

if __name__ == "__main__":
    install_pytorch()
'''
    
    with open("install_downloaded.py", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    print("✅ 创建了自动安装脚本: install_downloaded.py")

if __name__ == "__main__":
    success = download_pytorch_cuda()
    if success:
        create_install_script()
    
    print("\\n完成!")
