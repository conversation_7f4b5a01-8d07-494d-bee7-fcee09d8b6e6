"""
一键安装CUDA版本PyTorch
"""

import subprocess
import sys
import os

def run_command(command):
    """运行命令并显示输出"""
    print(f"执行: {command}")
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: {e}")
        print(f"输出: {e.stdout}")
        print(f"错误: {e.stderr}")
        return False

def install_cuda_pytorch():
    """安装CUDA版本的PyTorch"""
    
    print("=== 安装CUDA版本PyTorch ===")
    
    # 1. 卸载现有版本
    print("\n1. 卸载CPU版本...")
    commands_uninstall = [
        "pip uninstall torch -y",
        "pip uninstall torchvision -y", 
        "pip uninstall torchaudio -y"
    ]
    
    for cmd in commands_uninstall:
        run_command(cmd)
    
    # 2. 安装CUDA版本
    print("\n2. 安装CUDA版本...")
    
    # 尝试多个安装源
    install_commands = [
        # 官方源 (CUDA 11.8)
        "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118",
        
        # 备用: CUDA 12.1
        "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121",
        
        # 备用: 清华镜像
        "pip install torch torchvision torchaudio -i https://pypi.tuna.tsinghua.edu.cn/simple/"
    ]
    
    for i, cmd in enumerate(install_commands, 1):
        print(f"\n尝试方案 {i}...")
        if run_command(cmd):
            print(f"✅ 方案 {i} 安装成功!")
            break
        else:
            print(f"❌ 方案 {i} 失败，尝试下一个...")
    
    # 3. 验证安装
    print("\n3. 验证安装...")
    verification_code = '''
import torch
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA版本: {torch.version.cuda}")
    print(f"GPU: {torch.cuda.get_device_name(0)}")
    print("✅ CUDA安装成功!")
else:
    print("❌ CUDA不可用")
'''
    
    try:
        exec(verification_code)
        return True
    except Exception as e:
        print(f"验证失败: {e}")
        return False

def main():
    print("CUDA PyTorch 自动安装程序")
    print("适用于RTX 3060等NVIDIA显卡")
    
    # 检查是否在虚拟环境中
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ 检测到虚拟环境")
    else:
        print("⚠️ 建议在虚拟环境中安装")
    
    # 开始安装
    success = install_cuda_pytorch()
    
    if success:
        print("\n🎉 安装完成!")
        print("现在可以运行:")
        print("  python check_cuda.py  # 检查CUDA环境")
        print("  python gpu_train.py   # 开始GPU训练")
    else:
        print("\n❌ 安装失败")
        print("手动安装命令:")
        print("  pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")

if __name__ == "__main__":
    main()
