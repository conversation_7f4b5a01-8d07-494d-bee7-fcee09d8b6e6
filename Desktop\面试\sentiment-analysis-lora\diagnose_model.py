"""
模型诊断脚本 - 检查训练后的模型状态
"""

import os
import sys
import torch
import numpy as np
import json
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from peft import PeftModel
import warnings
warnings.filterwarnings('ignore')

sys.path.append('./src')

def diagnose_model(model_path):
    """诊断模型状态"""
    
    print(f"=== 诊断模型: {model_path} ===\n")
    
    # 1. 检查文件结构
    print("1. 检查文件结构:")
    required_files = [
        "adapter_config.json",
        "adapter_model.safetensors", 
        "tokenizer.json",
        "training_config.json"
    ]
    
    for file in required_files:
        file_path = os.path.join(model_path, file)
        exists = "✅" if os.path.exists(file_path) else "❌"
        print(f"   {exists} {file}")
    
    # 2. 检查配置文件
    print("\n2. 检查LoRA配置:")
    config_path = os.path.join(model_path, "adapter_config.json")
    if os.path.exists(config_path):
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        print(f"   基础模型: {config.get('base_model_name_or_path', 'N/A')}")
        print(f"   LoRA rank: {config.get('r', 'N/A')}")
        print(f"   LoRA alpha: {config.get('lora_alpha', 'N/A')}")
        print(f"   目标模块: {config.get('target_modules', 'N/A')}")
        print(f"   保存模块: {config.get('modules_to_save', 'N/A')}")
        print(f"   推理模式: {config.get('inference_mode', 'N/A')}")
    
    # 3. 检查训练配置
    print("\n3. 检查训练配置:")
    train_config_path = os.path.join(model_path, "training_config.json")
    if os.path.exists(train_config_path):
        with open(train_config_path, 'r') as f:
            train_config = json.load(f)
        
        for key, value in train_config.items():
            print(f"   {key}: {value}")
    
    # 4. 尝试加载模型
    print("\n4. 尝试加载模型:")
    try:
        # 加载tokenizer
        print("   加载tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        print("   ✅ tokenizer加载成功")
        
        # 加载基础模型
        print("   加载基础模型...")
        base_model = AutoModelForSequenceClassification.from_pretrained(
            "bert-base-chinese",
            num_labels=3,
            torch_dtype=torch.float32  # 使用float32避免精度问题
        )
        print("   ✅ 基础模型加载成功")
        
        # 加载LoRA权重
        print("   加载LoRA权重...")
        model = PeftModel.from_pretrained(base_model, model_path)
        print("   ✅ LoRA权重加载成功")
        
        # 检查模型参数
        print("\n5. 检查模型参数:")
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"   总参数: {total_params:,}")
        print(f"   可训练参数: {trainable_params:,}")
        print(f"   可训练比例: {trainable_params/total_params*100:.2f}%")
        
        # 6. 测试预测
        print("\n6. 测试模型预测:")
        model.eval()
        device = "cuda" if torch.cuda.is_available() else "cpu"
        model = model.to(device)
        
        test_text = "这个产品很好"
        inputs = tokenizer(
            test_text,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=512
        ).to(device)
        
        with torch.no_grad():
            outputs = model(**inputs)
            logits = outputs.logits
            probabilities = torch.softmax(logits, dim=-1)
            prediction = torch.argmax(logits, dim=-1)
        
        print(f"   测试文本: {test_text}")
        print(f"   logits: {logits.cpu().numpy()}")
        print(f"   概率: {probabilities.cpu().numpy()}")
        print(f"   预测: {prediction.cpu().numpy()}")
        
        # 检查是否有异常值
        if torch.isnan(logits).any():
            print("   ❌ 发现NaN值在logits中")
        elif torch.isinf(logits).any():
            print("   ❌ 发现无穷值在logits中")
        else:
            print("   ✅ 模型输出正常")
            
        return True
        
    except Exception as e:
        print(f"   ❌ 模型加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_best_checkpoint(model_path):
    """检查最佳检查点"""
    
    print(f"\n=== 检查最佳检查点 ===")
    
    # 查找trainer_state.json
    trainer_state_path = os.path.join(model_path, "trainer_state.json")
    if not os.path.exists(trainer_state_path):
        # 尝试在checkpoint目录中查找
        for item in os.listdir(model_path):
            if item.startswith("checkpoint-"):
                checkpoint_path = os.path.join(model_path, item, "trainer_state.json")
                if os.path.exists(checkpoint_path):
                    trainer_state_path = checkpoint_path
                    break
    
    if os.path.exists(trainer_state_path):
        with open(trainer_state_path, 'r') as f:
            trainer_state = json.load(f)
        
        print(f"最佳检查点: {trainer_state.get('best_model_checkpoint', 'N/A')}")
        print(f"最佳指标: {trainer_state.get('best_metric', 'N/A')}")
        print(f"总步数: {trainer_state.get('global_step', 'N/A')}")
        print(f"轮数: {trainer_state.get('epoch', 'N/A')}")
        
        # 检查训练历史中的异常
        log_history = trainer_state.get('log_history', [])
        nan_count = sum(1 for log in log_history if 'grad_norm' in log and str(log['grad_norm']).lower() == 'nan')
        zero_loss_count = sum(1 for log in log_history if 'loss' in log and log['loss'] == 0.0)
        
        print(f"NaN梯度次数: {nan_count}")
        print(f"零损失次数: {zero_loss_count}")
        
        if nan_count > 0:
            print("⚠️  训练过程中出现梯度爆炸/消失问题")
        if zero_loss_count > 0:
            print("⚠️  训练过程中出现损失为零的异常")
            
        return trainer_state.get('best_model_checkpoint')
    else:
        print("未找到trainer_state.json文件")
        return None

def main():
    """主函数"""
    
    # 检查可用的模型路径
    possible_paths = ["./gpu_models", "./models", "./fast_models"]
    
    for model_path in possible_paths:
        if os.path.exists(model_path) and os.path.exists(os.path.join(model_path, "adapter_config.json")):
            print(f"找到模型: {model_path}")
            
            # 诊断主模型
            success = diagnose_model(model_path)
            
            # 检查最佳检查点
            best_checkpoint = check_best_checkpoint(model_path)
            
            if best_checkpoint and os.path.exists(best_checkpoint):
                print(f"\n尝试诊断最佳检查点: {best_checkpoint}")
                diagnose_model(best_checkpoint)
            
            print("\n" + "="*60)
            
    print("\n=== 诊断建议 ===")
    print("如果发现以下问题:")
    print("1. NaN梯度 -> 学习率过高，建议降低到1e-5")
    print("2. 零损失 -> 数值不稳定，建议使用float32精度")
    print("3. 模型输出异常 -> 重新训练模型")
    print("4. 分类器权重未初始化 -> 检查modules_to_save配置")

if __name__ == "__main__":
    main()
