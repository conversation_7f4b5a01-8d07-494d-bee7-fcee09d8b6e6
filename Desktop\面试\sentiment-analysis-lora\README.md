# 🎭 中文情感分析系统

基于LoRA微调技术的中文情感分析项目，使用BERT模型进行高效微调，支持正面、负面、中性三类情感分类。

## 🌟 项目亮点

- **高效微调**: 使用LoRA技术，仅训练1-2%的参数，大幅降低计算成本
- **现代技术栈**: 基于Hugging Face生态，使用最新的PEFT库
- **完整流程**: 包含数据处理、模型训练、评估、推理的完整MLOps流程
- **友好界面**: 提供Gradio Web应用，支持单文本和批量分析
- **实用性强**: 适用于电商评论、社交媒体等实际场景

## 🛠️ 技术栈

- **基础模型**: BERT-base-Chinese
- **微调技术**: LoRA (Low-Rank Adaptation)
- **深度学习框架**: PyTorch + Transformers
- **高效微调**: PEFT (Parameter-Efficient Fine-Tuning)
- **数据处理**: pandas, jieba
- **可视化**: matplotlib, seaborn
- **Web应用**: Gradio

## 📁 项目结构

```
sentiment-analysis-lora/
├── data/                    # 数据集目录
├── models/                  # 保存的模型
├── notebooks/              # Jupyter实验记录
├── src/                    # 源代码
│   ├── data_process.py     # 数据预处理模块
│   ├── train.py           # 训练脚本
│   ├── evaluate.py        # 评估脚本
│   └── inference.py       # 推理脚本
├── app.py                 # Gradio Web应用
├── requirements.txt       # 依赖包列表
└── README.md             # 项目说明
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <your-repo-url>
cd sentiment-analysis-lora

# 安装依赖
pip install -r requirements.txt
```

### 2. 数据准备

项目包含示例数据，也可以使用自己的数据集：

```python
# 数据格式 (CSV)
text,label
"这个产品质量很好，非常满意！",1
"服务态度差，不推荐购买",0
"价格合理，性价比不错",1
```

标签说明：
- 0: 负面情感
- 1: 正面情感  
- 2: 中性情感

### 3. 模型训练

```bash
cd src
python train.py
```

训练参数可在脚本中调整：
- `num_epochs`: 训练轮数 (默认3)
- `batch_size`: 批次大小 (默认8)
- `learning_rate`: 学习率 (默认2e-4)

### 4. 模型评估

```bash
python evaluate.py
```

评估结果包括：
- 准确率、精确率、召回率、F1分数
- 混淆矩阵可视化
- 详细分类报告

### 5. 模型推理

```bash
# 交互式预测
python inference.py

# 批量预测演示
python inference.py demo
```

### 6. Web应用

```bash
python app.py
```

访问 http://localhost:7860 使用Web界面

## 📊 性能表现

基于示例数据集的测试结果：

| 指标 | 数值 |
|------|------|
| 准确率 | 85%+ |
| F1分数 | 83%+ |
| 训练时间 | ~5分钟 |
| 推理速度 | ~100ms/文本 |

## 🔧 LoRA配置

```python
lora_config = LoraConfig(
    task_type=TaskType.SEQ_CLS,
    r=16,                    # rank
    lora_alpha=32,           # scaling factor
    lora_dropout=0.1,        # dropout
    target_modules=["query", "value", "key", "dense"],
    bias="none"
)
```

## 💡 使用场景

- **电商平台**: 商品评论情感分析
- **社交媒体**: 用户评论监控
- **客服系统**: 客户反馈分类
- **市场调研**: 品牌舆情分析

## 🎯 项目优势

### 技术优势
- **参数高效**: LoRA只训练少量参数，节省计算资源
- **效果显著**: 在情感分析任务上表现优秀
- **易于部署**: 模型体积小，推理速度快
- **可扩展性**: 框架可轻松适配其他NLP任务

### 工程优势
- **代码规范**: 模块化设计，易于维护
- **完整流程**: 从数据处理到部署的完整pipeline
- **可视化**: 丰富的图表展示训练和评估结果
- **用户友好**: 提供多种使用方式

## 📈 扩展方向

- [ ] 支持更多情感细粒度分类
- [ ] 集成更多预训练模型
- [ ] 添加模型解释性分析
- [ ] 支持实时流式处理
- [ ] 部署到云平台

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License

## 👨‍💻 作者

基于LoRA高效微调技术实现的中文情感分析系统

---

⭐ 如果这个项目对你有帮助，请给个Star！
