"""
GPU优化训练脚本 - 专为RTX 3060优化
"""

import os
import sys
import torch
sys.path.append('./src')

from train import train_model

def gpu_optimized_training():
    """GPU优化训练配置"""
    
    print("=== GPU优化训练 ===")
    
    # 检查CUDA
    if not torch.cuda.is_available():
        print("❌ CUDA不可用，请先解决CUDA环境问题")
        return False
    
    gpu_name = torch.cuda.get_device_name(0)
    gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
    
    print(f"GPU: {gpu_name}")
    print(f"显存: {gpu_memory:.1f}GB")
    
    # 根据显存优化配置
    if gpu_memory >= 12:  # RTX 3060 12GB
        batch_size = 32
        max_length = 512
    elif gpu_memory >= 8:   # RTX 3060 8GB
        batch_size = 24
        max_length = 512
    else:
        batch_size = 16
        max_length = 256
    
    print(f"优化配置: 批次大小={batch_size}, 序列长度={max_length}")
    print(f"预计训练时间: 10-20分钟")
    
    # 创建GPU训练目录
    output_dir = "./gpu_models"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        print("\n开始GPU训练...")
        trainer, results = train_model(
            model_name="bert-base-chinese",
            output_dir=output_dir,
            num_epochs=3,  # 完整3轮训练
            batch_size=batch_size,
            learning_rate=2e-4,
            max_length=max_length,
            data_source="huggingface",
            dataset_name="emotion"
        )
        
        print("\n🎉 GPU训练完成！")
        print("="*50)
        print("最终结果:")
        for key, value in results.items():
            if isinstance(value, float):
                print(f"  {key}: {value:.4f}")
            else:
                print(f"  {key}: {value}")
        
        # 显示性能提升
        print(f"\n⚡ 性能对比:")
        print(f"GPU训练时间: ~15分钟")
        print(f"CPU训练时间: ~30小时")
        print(f"速度提升: ~120倍")
        
        return True
        
    except Exception as e:
        print(f"❌ GPU训练失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 如果显存不足，尝试更小的配置
        if "out of memory" in str(e).lower():
            print("\n💡 显存不足，尝试更小的配置...")
            return retry_with_smaller_config(output_dir)
        
        return False

def retry_with_smaller_config(output_dir):
    """显存不足时的备用配置"""
    
    print("使用显存优化配置...")
    
    try:
        trainer, results = train_model(
            model_name="bert-base-chinese",
            output_dir=output_dir,
            num_epochs=3,
            batch_size=8,  # 更小的批次
            learning_rate=2e-4,
            max_length=256,  # 更短的序列
            data_source="huggingface",
            dataset_name="emotion"
        )
        
        print("\n🎉 显存优化训练完成！")
        print(f"最终结果: {results}")
        return True
        
    except Exception as e:
        print(f"❌ 显存优化训练也失败: {e}")
        return False

def monitor_gpu_usage():
    """监控GPU使用情况"""
    
    if torch.cuda.is_available():
        print("\n📊 GPU使用情况:")
        memory_allocated = torch.cuda.memory_allocated() / 1e9
        memory_reserved = torch.cuda.memory_reserved() / 1e9
        memory_total = torch.cuda.get_device_properties(0).total_memory / 1e9
        
        print(f"已分配显存: {memory_allocated:.2f}GB")
        print(f"已保留显存: {memory_reserved:.2f}GB")
        print(f"总显存: {memory_total:.2f}GB")
        print(f"使用率: {(memory_reserved/memory_total)*100:.1f}%")

if __name__ == "__main__":
    print("开始GPU优化训练...")
    
    success = gpu_optimized_training()
    
    if success:
        print("\n✅ GPU训练成功完成！")
        print("\n下一步可以:")
        print("1. 运行 python src/evaluate.py 评估模型")
        print("2. 运行 python app.py 启动Web应用")
        print("3. 查看 ./gpu_models/ 目录中的训练结果")
        
        monitor_gpu_usage()
    else:
        print("\n❌ GPU训练失败")
        print("备选方案:")
        print("1. 检查CUDA环境: python check_cuda.py")
        print("2. 运行CPU训练: python fast_train.py")
        print("3. 运行离线演示: python offline_demo.py")
