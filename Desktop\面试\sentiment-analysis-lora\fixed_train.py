"""
修复版训练脚本 - 解决NaN和数值不稳定问题
"""

import os
import sys
import torch
import json
import warnings
warnings.filterwarnings('ignore')

sys.path.append('./src')

from transformers import (
    AutoTokenizer, 
    AutoModelForSequenceClassification,
    TrainingArguments, 
    Trainer,
    DataCollatorWithPadding
)
from peft import LoraConfig, get_peft_model, TaskType
from data_process import prepare_data, create_datasets
from train import compute_metrics

def create_stable_lora_model(model_name: str = "bert-base-chinese", num_labels: int = 3):
    """创建稳定的LoRA微调模型"""
    
    print("创建稳定的LoRA模型...")
    
    # 加载基础模型 - 使用float32确保数值稳定性
    model = AutoModelForSequenceClassification.from_pretrained(
        model_name,
        num_labels=num_labels,
        torch_dtype=torch.float32,  # 强制使用float32
        attention_probs_dropout_prob=0.1,  # 添加dropout防止过拟合
        hidden_dropout_prob=0.1
    )
    
    # 稳定的LoRA配置
    lora_config = LoraConfig(
        task_type=TaskType.SEQ_CLS,
        r=8,  # 降低rank避免过拟合
        lora_alpha=16,  # 降低alpha确保稳定性
        lora_dropout=0.2,  # 增加dropout
        target_modules=["query", "value"],  # 只针对关键模块，减少复杂性
        bias="none",
        use_rslora=False,
        init_lora_weights=True,
        modules_to_save=["classifier"]  # 确保分类器被保存
    )
    
    # 应用LoRA
    model = get_peft_model(model, lora_config)
    
    # 打印可训练参数
    model.print_trainable_parameters()
    
    return model

def stable_training():
    """稳定的训练配置"""
    
    print("=== 修复版训练 - 解决NaN问题 ===")
    
    # 检查设备
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"使用设备: {device}")
    
    # 创建输出目录
    output_dir = "./fixed_models"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        print("准备数据...")
        # 准备数据
        train_df, test_df = prepare_data(
            data_source="huggingface",
            dataset_name="emotion"
        )
        
        train_dataset, test_dataset, tokenizer = create_datasets(
            train_df, test_df, "bert-base-chinese", max_length=256
        )
        
        print("创建模型...")
        # 创建稳定的模型
        model = create_stable_lora_model("bert-base-chinese", num_labels=3)
        
        # 数据整理器
        data_collator = DataCollatorWithPadding(tokenizer=tokenizer)
        
        print("配置训练参数...")
        # 稳定的训练参数
        training_args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=2,  # 减少轮数避免过拟合
            per_device_train_batch_size=8,  # 减小批次大小
            per_device_eval_batch_size=8,
            warmup_steps=100,
            weight_decay=0.01,
            logging_dir=f"{output_dir}/logs",
            logging_steps=50,
            eval_strategy="steps",
            eval_steps=200,
            save_strategy="steps", 
            save_steps=200,
            load_best_model_at_end=True,
            metric_for_best_model="f1",
            greater_is_better=True,
            learning_rate=1e-5,  # 大幅降低学习率
            lr_scheduler_type="linear",  # 使用线性调度器
            fp16=False,  # 禁用fp16确保数值稳定
            bf16=False,  # 禁用bf16
            dataloader_pin_memory=False,
            remove_unused_columns=False,
            report_to=None,
            save_total_limit=3,
            seed=42,
            max_grad_norm=1.0,  # 添加梯度裁剪
            gradient_accumulation_steps=2,  # 梯度累积
            dataloader_num_workers=0,  # 避免多进程问题
            # 添加稳定性选项
            skip_memory_metrics=True,
            disable_tqdm=False
        )
        
        print("创建训练器...")
        # 创建训练器
        trainer = Trainer(
            model=model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=test_dataset,
            tokenizer=tokenizer,
            data_collator=data_collator,
            compute_metrics=compute_metrics,
        )
        
        print("开始训练...")
        print("配置摘要:")
        print(f"  学习率: {training_args.learning_rate}")
        print(f"  批次大小: {training_args.per_device_train_batch_size}")
        print(f"  训练轮数: {training_args.num_train_epochs}")
        print(f"  梯度裁剪: {training_args.max_grad_norm}")
        print(f"  数值精度: float32")
        
        # 训练模型
        trainer.train()
        
        print("保存模型...")
        # 保存模型
        trainer.save_model()
        tokenizer.save_pretrained(output_dir)
        
        # 保存训练配置
        config = {
            "model_name": "bert-base-chinese",
            "num_epochs": training_args.num_train_epochs,
            "batch_size": training_args.per_device_train_batch_size,
            "learning_rate": training_args.learning_rate,
            "max_length": 256,
            "data_source": "huggingface",
            "dataset_name": "emotion",
            "train_size": len(train_dataset),
            "test_size": len(test_dataset),
            "fixes_applied": [
                "降低学习率到1e-5",
                "使用float32精度",
                "添加梯度裁剪",
                "减小LoRA rank",
                "增加dropout",
                "简化target_modules"
            ]
        }
        
        with open(f"{output_dir}/training_config.json", "w", encoding="utf-8") as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print("进行最终评估...")
        eval_results = trainer.evaluate()
        
        print("\n🎉 修复版训练完成！")
        print("="*50)
        print("最终结果:")
        for key, value in eval_results.items():
            if isinstance(value, float):
                print(f"  {key}: {value:.4f}")
            else:
                print(f"  {key}: {value}")
        
        # 快速测试模型是否正常
        print("\n快速测试模型输出...")
        model.eval()
        test_text = "这个产品很好"
        inputs = tokenizer(
            test_text,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=256
        )
        
        with torch.no_grad():
            outputs = model(**inputs)
            logits = outputs.logits
            
        if torch.isnan(logits).any():
            print("❌ 模型仍然输出NaN，需要进一步调试")
        else:
            probabilities = torch.softmax(logits, dim=-1)
            prediction = torch.argmax(logits, dim=-1)
            print(f"✅ 模型输出正常")
            print(f"   logits: {logits.detach().numpy()}")
            print(f"   概率: {probabilities.detach().numpy()}")
            print(f"   预测: {prediction.detach().numpy()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = stable_training()
    
    if success:
        print("\n✅ 建议下一步:")
        print("1. 运行: python src/evaluate.py 测试修复后的模型")
        print("2. 如果仍有问题，可以进一步降低学习率到5e-6")
        print("3. 模型保存在 ./fixed_models 目录")
    else:
        print("\n❌ 训练失败，可能需要:")
        print("1. 检查CUDA环境")
        print("2. 确保有足够的内存")
        print("3. 检查数据集是否正确加载")
