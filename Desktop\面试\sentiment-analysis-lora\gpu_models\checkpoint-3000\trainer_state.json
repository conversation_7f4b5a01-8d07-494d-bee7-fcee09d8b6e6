{"best_global_step": 1000, "best_metric": 0.35408923279552185, "best_model_checkpoint": "./gpu_models\\checkpoint-1000", "epoch": 3.0, "eval_steps": 500, "global_step": 3000, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.05, "grad_norm": NaN, "learning_rate": 9.8e-05, "loss": 0.647, "step": 50}, {"epoch": 0.1, "grad_norm": NaN, "learning_rate": 0.00019800000000000002, "loss": 0.0, "step": 100}, {"epoch": 0.15, "grad_norm": NaN, "learning_rate": 0.0001998591477024944, "loss": 0.0, "step": 150}, {"epoch": 0.2, "grad_norm": NaN, "learning_rate": 0.00019942544999705569, "loss": 0.0, "step": 200}, {"epoch": 0.25, "grad_norm": NaN, "learning_rate": 0.00019870012031599407, "loss": 0.0, "step": 250}, {"epoch": 0.3, "grad_norm": NaN, "learning_rate": 0.00019768528617623675, "loss": 0.0, "step": 300}, {"epoch": 0.35, "grad_norm": NaN, "learning_rate": 0.00019638392426116926, "loss": 0.0, "step": 350}, {"epoch": 0.4, "grad_norm": NaN, "learning_rate": 0.0001947998516895103, "loss": 0.0, "step": 400}, {"epoch": 0.45, "grad_norm": NaN, "learning_rate": 0.00019293771481904463, "loss": 0.0, "step": 450}, {"epoch": 0.5, "grad_norm": NaN, "learning_rate": 0.00019080297561805534, "loss": 0.0, "step": 500}, {"epoch": 0.55, "grad_norm": NaN, "learning_rate": 0.0001884018956444297, "loss": 0.0, "step": 550}, {"epoch": 0.6, "grad_norm": NaN, "learning_rate": 0.000185741517679431, "loss": 0.0, "step": 600}, {"epoch": 0.65, "grad_norm": NaN, "learning_rate": 0.0001828296450700078, "loss": 0.0, "step": 650}, {"epoch": 0.7, "grad_norm": NaN, "learning_rate": 0.00017967481884023257, "loss": 0.0, "step": 700}, {"epoch": 0.75, "grad_norm": NaN, "learning_rate": 0.00017628629263900675, "loss": 0.0, "step": 750}, {"epoch": 0.8, "grad_norm": NaN, "learning_rate": 0.00017267400559751396, "loss": 0.0, "step": 800}, {"epoch": 0.85, "grad_norm": NaN, "learning_rate": 0.00016884855317603588, "loss": 0.0, "step": 850}, {"epoch": 0.9, "grad_norm": NaN, "learning_rate": 0.0001648211560856415, "loss": 0.0, "step": 900}, {"epoch": 0.95, "grad_norm": NaN, "learning_rate": 0.00016060362737590843, "loss": 0.0, "step": 950}, {"epoch": 1.0, "grad_norm": NaN, "learning_rate": 0.00015620833778521307, "loss": 0.0, "step": 1000}, {"epoch": 1.0, "eval_accuracy": 0.5185, "eval_f1": 0.35408923279552185, "eval_loss": NaN, "eval_precision": 0.26884225, "eval_recall": 0.5185, "eval_runtime": 14.1627, "eval_samples_per_second": 141.216, "eval_steps_per_second": 8.826, "step": 1000}, {"epoch": 1.05, "grad_norm": NaN, "learning_rate": 0.00015164817945522344, "loss": 0.0, "step": 1050}, {"epoch": 1.1, "grad_norm": NaN, "learning_rate": 0.00014693652811602635, "loss": 0.0, "step": 1100}, {"epoch": 1.15, "grad_norm": NaN, "learning_rate": 0.00014208720385280673, "loss": 0.0, "step": 1150}, {"epoch": 1.2, "grad_norm": NaN, "learning_rate": 0.00013711443056915612, "loss": 0.0, "step": 1200}, {"epoch": 1.25, "grad_norm": NaN, "learning_rate": 0.0001320327942659133, "loss": 0.0, "step": 1250}, {"epoch": 1.3, "grad_norm": NaN, "learning_rate": 0.00012685720025791005, "loss": 0.0, "step": 1300}, {"epoch": 1.35, "grad_norm": NaN, "learning_rate": 0.00012160282945411513, "loss": 0.0, "step": 1350}, {"epoch": 1.4, "grad_norm": NaN, "learning_rate": 0.00011628509382941233, "loss": 0.0, "step": 1400}, {"epoch": 1.45, "grad_norm": NaN, "learning_rate": 0.00011091959121862279, "loss": 0.0, "step": 1450}, {"epoch": 1.5, "grad_norm": NaN, "learning_rate": 0.00010552205956536803, "loss": 0.0, "step": 1500}, {"epoch": 1.55, "grad_norm": NaN, "learning_rate": 0.0001001083307599696, "loss": 0.0, "step": 1550}, {"epoch": 1.6, "grad_norm": NaN, "learning_rate": 9.469428420178698e-05, "loss": 0.0, "step": 1600}, {"epoch": 1.65, "grad_norm": NaN, "learning_rate": 8.929580022220349e-05, "loss": 0.0, "step": 1650}, {"epoch": 1.7, "grad_norm": NaN, "learning_rate": 8.392871350487781e-05, "loss": 0.0, "step": 1700}, {"epoch": 1.75, "grad_norm": NaN, "learning_rate": 7.860876663988885e-05, "loss": 0.0, "step": 1750}, {"epoch": 1.8, "grad_norm": NaN, "learning_rate": 7.335156394800652e-05, "loss": 0.0, "step": 1800}, {"epoch": 1.85, "grad_norm": NaN, "learning_rate": 6.817252571053035e-05, "loss": 0.0, "step": 1850}, {"epoch": 1.9, "grad_norm": NaN, "learning_rate": 6.308684293894747e-05, "loss": 0.0, "step": 1900}, {"epoch": 1.95, "grad_norm": NaN, "learning_rate": 5.810943281707863e-05, "loss": 0.0, "step": 1950}, {"epoch": 2.0, "grad_norm": NaN, "learning_rate": 5.3254894946407884e-05, "loss": 0.0, "step": 2000}, {"epoch": 2.0, "eval_accuracy": 0.5185, "eval_f1": 0.35408923279552185, "eval_loss": NaN, "eval_precision": 0.26884225, "eval_recall": 0.5185, "eval_runtime": 14.1911, "eval_samples_per_second": 140.934, "eval_steps_per_second": 8.808, "step": 2000}, {"epoch": 2.05, "grad_norm": NaN, "learning_rate": 4.853746852293583e-05, "loss": 0.0, "step": 2050}, {"epoch": 2.1, "grad_norm": NaN, "learning_rate": 4.39709905711645e-05, "loss": 0.0, "step": 2100}, {"epoch": 2.15, "grad_norm": NaN, "learning_rate": 3.9568855357720394e-05, "loss": 0.0, "step": 2150}, {"epoch": 2.2, "grad_norm": NaN, "learning_rate": 3.534397510366242e-05, "loss": 0.0, "step": 2200}, {"epoch": 2.25, "grad_norm": NaN, "learning_rate": 3.1308742110713165e-05, "loss": 0.0, "step": 2250}, {"epoch": 2.3, "grad_norm": NaN, "learning_rate": 2.7474992412502553e-05, "loss": 0.0, "step": 2300}, {"epoch": 2.35, "grad_norm": NaN, "learning_rate": 2.385397105744187e-05, "loss": 0.0, "step": 2350}, {"epoch": 2.4, "grad_norm": NaN, "learning_rate": 2.04562991250593e-05, "loss": 0.0, "step": 2400}, {"epoch": 2.45, "grad_norm": NaN, "learning_rate": 1.7291942572543807e-05, "loss": 0.0, "step": 2450}, {"epoch": 2.5, "grad_norm": NaN, "learning_rate": 1.4370183002875459e-05, "loss": 0.0, "step": 2500}, {"epoch": 2.55, "grad_norm": NaN, "learning_rate": 1.169959044028467e-05, "loss": 0.0, "step": 2550}, {"epoch": 2.6, "grad_norm": NaN, "learning_rate": 9.287998192894597e-06, "loss": 0.0, "step": 2600}, {"epoch": 2.65, "grad_norm": NaN, "learning_rate": 7.142479876278796e-06, "loss": 0.0, "step": 2650}, {"epoch": 2.7, "grad_norm": NaN, "learning_rate": 5.269328665327855e-06, "loss": 0.0, "step": 2700}, {"epoch": 2.75, "grad_norm": NaN, "learning_rate": 3.6740388352832667e-06, "loss": 0.0, "step": 2750}, {"epoch": 2.8, "grad_norm": NaN, "learning_rate": 2.361289646081377e-06, "loss": 0.0, "step": 2800}, {"epoch": 2.85, "grad_norm": NaN, "learning_rate": 1.334931617277857e-06, "loss": 0.0, "step": 2850}, {"epoch": 2.9, "grad_norm": NaN, "learning_rate": 5.979752338103661e-07, "loss": 0.0, "step": 2900}, {"epoch": 2.95, "grad_norm": NaN, "learning_rate": 1.5258211572756197e-07, "loss": 0.0, "step": 2950}, {"epoch": 3.0, "grad_norm": NaN, "learning_rate": 5.867778499757392e-11, "loss": 0.0, "step": 3000}, {"epoch": 3.0, "eval_accuracy": 0.5185, "eval_f1": 0.35408923279552185, "eval_loss": NaN, "eval_precision": 0.26884225, "eval_recall": 0.5185, "eval_runtime": 13.9956, "eval_samples_per_second": 142.902, "eval_steps_per_second": 8.931, "step": 3000}], "logging_steps": 50, "max_steps": 3000, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 6512393502720000.0, "train_batch_size": 16, "trial_name": null, "trial_params": null}