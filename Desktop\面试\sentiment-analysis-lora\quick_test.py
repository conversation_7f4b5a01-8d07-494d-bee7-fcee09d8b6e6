"""
快速测试脚本
验证整个训练流程是否正常工作
"""

import os
import sys
import torch

# 添加src目录到路径
sys.path.append('./src')

from train import train_model
from data_process import prepare_data, create_datasets

def quick_test():
    """快速测试训练流程"""
    
    print("=== 快速测试开始 ===")
    
    # 检查环境
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA设备: {torch.cuda.get_device_name()}")
        print(f"显存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f}GB")
    
    # 创建测试目录
    test_dir = "./test_models"
    os.makedirs(test_dir, exist_ok=True)
    
    print("\n=== 测试数据准备 ===")
    
    # 测试数据准备
    try:
        train_df, test_df = prepare_data(
            data_source="sample",  # 使用示例数据快速测试
        )
        print(f"✅ 数据准备成功: 训练{len(train_df)}条, 测试{len(test_df)}条")
        
        # 创建数据集
        train_dataset, test_dataset, tokenizer = create_datasets(
            train_df, test_df, 
            model_name="bert-base-chinese",
            max_length=128  # 减少长度以加快测试
        )
        print(f"✅ 数据集创建成功: 训练{len(train_dataset)}条, 测试{len(test_dataset)}条")
        
    except Exception as e:
        print(f"❌ 数据准备失败: {e}")
        return False
    
    print("\n=== 测试模型训练 ===")
    
    # 测试训练（快速配置）
    try:
        trainer, results = train_model(
            model_name="bert-base-chinese",
            output_dir=test_dir,
            num_epochs=1,  # 只训练1轮
            batch_size=2,  # 小批次
            learning_rate=2e-4,
            max_length=128,  # 短序列
            data_source="sample"
        )
        
        print(f"✅ 训练完成!")
        print(f"最终结果: {results}")
        
        # 检查模型文件
        model_files = os.listdir(test_dir)
        print(f"生成的文件: {model_files}")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_test():
    """清理测试文件"""
    import shutil
    test_dir = "./test_models"
    if os.path.exists(test_dir):
        shutil.rmtree(test_dir)
        print("🧹 测试文件已清理")

if __name__ == "__main__":
    print("开始快速测试...")
    
    success = quick_test()
    
    if success:
        print("\n🎉 快速测试通过！")
        print("现在可以开始正式训练了")
        
        # 询问是否清理测试文件
        response = input("\n是否清理测试文件? (y/n): ").lower()
        if response == 'y':
            cleanup_test()
    else:
        print("\n❌ 快速测试失败，请检查错误信息")
    
    print("\n测试完成！")
