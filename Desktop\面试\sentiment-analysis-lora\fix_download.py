"""
修复模型下载问题
使用国内镜像和优化配置
"""

import os
import sys

# 设置环境变量使用国内镜像
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
os.environ['HF_HUB_ENABLE_HF_TRANSFER'] = '1'

# 添加src目录
sys.path.append('./src')

import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from data_process import prepare_data, create_datasets

def download_model_with_mirror():
    """使用镜像下载模型"""
    
    print("=== 使用国内镜像下载模型 ===")
    print("镜像地址: https://hf-mirror.com")
    
    try:
        # 下载tokenizer（通常很快）
        print("1. 下载tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(
            "bert-base-chinese",
            cache_dir="./cache",
            force_download=False  # 使用缓存
        )
        print("✅ Tokenizer下载完成")
        
        # 下载模型（可能较慢）
        print("2. 下载模型...")
        model = AutoModelForSequenceClassification.from_pretrained(
            "bert-base-chinese",
            num_labels=3,
            cache_dir="./cache",
            force_download=False,  # 使用缓存
            torch_dtype=torch.float32,  # 使用float32避免兼容性问题
            low_cpu_mem_usage=True  # 降低内存使用
        )
        print("✅ 模型下载完成")
        
        return tokenizer, model
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return None, None

def quick_train_test():
    """快速训练测试"""
    
    print("\n=== 快速训练测试 ===")
    
    # 下载模型
    tokenizer, model = download_model_with_mirror()
    if tokenizer is None or model is None:
        print("模型下载失败，无法继续")
        return False
    
    # 准备数据
    print("3. 准备数据...")
    train_df, test_df = prepare_data(data_source="sample")
    
    # 创建小数据集进行快速测试
    train_df = train_df.head(10)  # 只用10条数据
    test_df = test_df.head(4)     # 只用4条数据
    
    print(f"使用小数据集: 训练{len(train_df)}条, 测试{len(test_df)}条")
    
    # 简单验证
    sample_text = train_df.iloc[0]['text']
    sample_label = train_df.iloc[0]['label']
    
    print(f"样本文本: {sample_text}")
    print(f"样本标签: {sample_label}")
    
    # 测试tokenizer
    inputs = tokenizer(sample_text, return_tensors="pt", max_length=128, truncation=True, padding=True)
    print(f"Token化结果: {inputs['input_ids'].shape}")
    
    # 测试模型前向传播
    with torch.no_grad():
        outputs = model(**inputs)
        logits = outputs.logits
        prediction = torch.argmax(logits, dim=-1)
        print(f"模型输出: {logits.shape}")
        print(f"预测结果: {prediction.item()}")
    
    print("✅ 快速测试通过！")
    return True

if __name__ == "__main__":
    print("开始修复下载问题...")
    
    # 创建缓存目录
    os.makedirs("./cache", exist_ok=True)
    
    success = quick_train_test()
    
    if success:
        print("\n🎉 问题已解决！")
        print("现在可以正常使用模型了")
        print("\n下一步:")
        print("1. 运行 python src/train.py 开始正式训练")
        print("2. 或者运行 python quick_test.py 进行完整测试")
    else:
        print("\n❌ 仍有问题，请检查网络连接")
        print("备选方案:")
        print("1. 使用手机热点")
        print("2. 使用VPN")
        print("3. 使用更小的模型如 hfl/chinese-bert-wwm-ext")
